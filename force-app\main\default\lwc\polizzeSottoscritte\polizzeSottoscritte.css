img.desaturate {
    filter: grayscale(100%);
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: url(desaturate.svg#greyscale);
    -webkit-filter: grayscale(1);
    filter: gray;
}

img.scale{
    display: block;
    margin-left: auto;
    margin-right: auto;
    scale: 85%;
    cursor: pointer;
}

/* Stili per le categorie di polizze */
.polizza-category {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    min-height: 150px;
}

.category-with-data {
    display: flex;
    align-items: center; /* centra verticalmente */
    gap: 10px; /* riduce lo spazio tra immagine e testo */
}

.category-no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    flex-grow: 1;
}

.policy-data {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 12px;
    min-width: 120px;
}

.data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2px 0;
}

.data-label {
    font-weight: 500;
    color: #3e3e3c;
    margin-right: 8px;
}

.data-value {
    font-weight: 600;
    color: #ee8433;
}

.category-label {
    margin-top: 10px;
    font-size: 14px;
}
