<template>
        <article class="slds-card">
            <div class="slds-card__header slds-grid slds-page-header">
                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                    <div class="slds-media__figure">
                        <span class="slds-icon_container slds-icon-custom-custom91" title="SearchDetails">
                            <lightning-icon icon-name="custom:custom91"></lightning-icon>
                        </span>
                    </div>
                    <div class="slds-media__body">
                        <h2 class="slds-card__header-title">
                                <span>Polizze Attive</span>
                        </h2>
                    </div>
                </header>
            </div>
            <div class="slds-card__body slds-card__body_inner">
                <div class="slds-grid">
                    <!-- Casa (Residential) -->
                    <div class="slds-col polizza-category">
                        <template lwc:if={residentialData}>
                            <div class="category-with-data">
                                <img src={icoCasa} alt="Residential" class={ResidentialClass} onclick={navigateToPolizza}>
                                <div class="policy-data">
                                    <div class="data-item">
                                        <span class="data-label">Attive:</span>
                                        <span class="data-value">{residentialData.count}</span>
                                    </div>
                                    <div class="data-item">
                                        <span class="data-label">Premio Lordo Mensile:</span>
                                        <span class="data-value">{residentialData.premioMensile} €</span>
                                    </div>
                                    <div class="data-item">
                                        <span class="data-label">Premio Lordo Annualizzato:</span>
                                        <span class="data-value">{residentialData.premioAnnualizzato} €</span>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template lwc:else>
                            <div class="category-no-data">
                                <img src={icoCasa} alt="Residential" class={ResidentialClass} onclick={navigateToPolizza}>
                            </div>
                        </template>
                        <p class="slds-text-align_center category-label"><b>Casa</b></p>
                    </div>

                    <!-- Salute e Infortuni (Medical) -->
                    <div class="slds-col polizza-category">
                        <template lwc:if={medicalData}>
                            <div class="category-with-data">
                                <img src={icoInfortuni} alt="Medical" class={MedicalClass} onclick={navigateToPolizza}>
                                <div class="policy-data">
                                    <div class="data-item">
                                        <span class="data-label">Attive:</span>
                                        <span class="data-value">{medicalData.count}</span>
                                    </div>
                                    <div class="data-item">
                                        <span class="data-label">Premio Lordo Mensile:</span>
                                        <span class="data-value">{medicalData.premioMensile} €</span>
                                    </div>
                                    <div class="data-item">
                                        <span class="data-label">Premio Lordo Annualizzato:</span>
                                        <span class="data-value">{medicalData.premioAnnualizzato} €</span>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template lwc:else>
                            <div class="category-no-data">
                                <img src={icoInfortuni} alt="Medical" class={MedicalClass} onclick={navigateToPolizza}>
                            </div>
                        </template>
                        <p class="slds-text-align_center category-label"><b>Salute e Infortuni</b></p>
                    </div>

                    <!-- Viaggi (Travel) -->
                    <div class="slds-col polizza-category">
                        <template lwc:if={travelData}>
                            <div class="category-with-data">
                                <img src={icoViaggi} alt="Travel" class={TravelClass} onclick={navigateToPolizza}>
                                <div class="policy-data">
                                    <div class="data-item">
                                        <span class="data-label">Attive:</span>
                                        <span class="data-value">{travelData.count}</span>
                                    </div>
                                    <div class="data-item">
                                        <span class="data-label">Premio Lordo:</span>
                                        <span class="data-value">{travelData.premioLordo} €</span>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template lwc:else>
                            <div class="category-no-data">
                                <img src={icoViaggi} alt="Travel" class={TravelClass} onclick={navigateToPolizza}>
                            </div>
                        </template>
                        <p class="slds-text-align_center category-label"><b>Viaggi</b></p>
                    </div>

                    <!-- Animali (Animal) -->
                    <template lwc:if={animali}>
                    <div class="slds-col polizza-category">
                        <template lwc:if={animalData}>
                            <div class="category-with-data">
                                <img src={icoAnimali} alt="Animal" class={AnimalClass} onclick={navigateToPolizza}>
                                <div class="policy-data">
                                    <div class="data-item">
                                        <span class="data-label">Attive:</span>
                                        <span class="data-value">{animalData.count}</span>
                                    </div>
                                    <div class="data-item">
                                        <span class="data-label">Premio Lordo Mensile:</span>
                                        <span class="data-value">{animalData.premioMensile} €</span>
                                    </div>
                                    <div class="data-item">
                                        <span class="data-label">Premio Lordo Annualizzato:</span>
                                        <span class="data-value">{animalData.premioAnnualizzato} €</span>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template lwc:else>
                            <div class="category-no-data">
                                <img src={icoAnimali} alt="Animal" class={AnimalClass} onclick={navigateToPolizza}>
                            </div>
                        </template>
                        <p class="slds-text-align_center category-label"><b>Animali</b></p>
                    </div>
                    </template>

                    <!-- Carrello Assicurato (Bill Protection) -->
                    <template lwc:if={carrelloProtetto}>
                    <div class="slds-col polizza-category">
                        <div class="category-no-data">
                            <img src={icoCarrello} alt="Bill Protection" class={BillProtectionClass} onclick={navigateToPolizza}>
                        </div>
                        <p class="slds-text-align_center category-label"><b>Carrello Assicurato</b></p>
                    </div>
                    </template>
                </div>
            </div>
        </article>

</template>