/**
 * Classe Controller per il componente Lightning Web Component (LWC) PolizzeSottoscritte.
 * @description Questa classe gestisce le operazioni di recupero delle polizze attive associate a un account.
 * <AUTHOR>
 * @since March 11, 2024
 * @version 1.0
 */
public without sharing class PolizzeSottoscritte_Controller {

    /**
     * @description Metodo statico che restituisce un elenco di categorie di polizze attive per un account specificato.
     * @param idAccount L'ID dell'account per il quale recuperare le polizze attive.
     * @return Un elenco di stringhe rappresentanti le categorie di polizze attive.
     */
    @AuraEnabled
    public static List<String> getPolizzeAttive(String idAccount) {
        List<String> output = new List<String>();
        // Query per recuperare il conteggio delle polizze attive per categoria
        List<AggregateResult> result = [SELECT Categoria__c, COUNT(Id) FROM Polizza__c WHERE Contraente__c = :idAccount AND Stato__c = 'Active' WITH SECURITY_ENFORCED GROUP BY Categoria__c];
        if (result.size() > 0) {
            // Loop sui risultati e aggiunta delle categorie all'output
            for (AggregateResult agg : result) {
                String categoria = String.valueOf(agg.get('Categoria__c'));
                switch on categoria {
                    when 'P/A' {
                        output.add('Medical');
                    }
                    when 'Residential - PL' {
                        output.add('Residential');
                    }
                    when 'Travel' {
                        output.add('Travel');
                    }
                    when 'CRDREG' {
                        output.add('Bill Protection');
                    }
                    when 'Pet' {
                        output.add('Animal');
                    }
                }
            }
            return output;
        } else {
            return null;
        }
    }

    /**
     * @description Metodo statico che restituisce i dettagli delle polizze attive per categoria con conteggi e importi premi.
     * @param idAccount L'ID dell'account per il quale recuperare i dettagli delle polizze attive.
     * @return Una mappa con i dettagli delle polizze per categoria.
     */
    @AuraEnabled
    public static Map<String, Map<String, Decimal>> getPolizzeAttiveDetails(String idAccount) {
        Map<String, Map<String, Decimal>> output = new Map<String, Map<String, Decimal>>();

        // Query per recuperare le polizze attive con tutti i campi necessari
        List<Polizza__c> polizze = [
            SELECT Id, Categoria__c, Importo_Premio_Lordo_Mensile__c,
                   Importo_Premio_Lordo_Annualizzato__c, Importo_Premio_Lordo__c
            FROM Polizza__c
            WHERE Contraente__c = :idAccount AND Stato__c = 'Active'
            WITH SECURITY_ENFORCED
        ];

        if (polizze.isEmpty()) {
            return output;
        }

        // Inizializza le mappe per ogni categoria
        Map<String, Map<String, Decimal>> categoryDataMaps = initializeCategoryMaps();

        // Elabora ogni polizza
        for (Polizza__c polizza : polizze) {
            processPolizzaByCategory(polizza, categoryDataMaps);
        }

        // Aggiungi solo le categorie che hanno polizze attive
        addActiveCategoriestoOutput(output, categoryDataMaps);

        return output;
    }

    /**
     * @description Inizializza le mappe per ogni categoria di polizza.
     * @return Una mappa contenente le mappe inizializzate per ogni categoria.
     */
    private static Map<String, Map<String, Decimal>> initializeCategoryMaps() {
        Map<String, Map<String, Decimal>> categoryMaps = new Map<String, Map<String, Decimal>>();

        categoryMaps.put('Medical', new Map<String, Decimal>{
            'count' => 0, 'premioMensile' => 0, 'premioAnnualizzato' => 0
        });
        categoryMaps.put('Residential', new Map<String, Decimal>{
            'count' => 0, 'premioMensile' => 0, 'premioAnnualizzato' => 0
        });
        categoryMaps.put('Travel', new Map<String, Decimal>{
            'count' => 0, 'premioLordo' => 0
        });
        categoryMaps.put('Animal', new Map<String, Decimal>{
            'count' => 0, 'premioMensile' => 0, 'premioAnnualizzato' => 0
        });

        return categoryMaps;
    }

    /**
     * @description Elabora una singola polizza e aggiorna i dati della categoria corrispondente.
     * @param polizza La polizza da elaborare.
     * @param categoryDataMaps Le mappe dei dati per categoria.
     */
    private static void processPolizzaByCategory(Polizza__c polizza, Map<String, Map<String, Decimal>> categoryDataMaps) {
        switch on polizza.Categoria__c {
            when 'P/A' {
                updateMedicalData(polizza, categoryDataMaps.get('Medical'));
            }
            when 'Residential - PL' {
                updateResidentialData(polizza, categoryDataMaps.get('Residential'));
            }
            when 'Travel' {
                updateTravelData(polizza, categoryDataMaps.get('Travel'));
            }
            when 'Pet' {
                updateAnimalData(polizza, categoryDataMaps.get('Animal'));
            }
        }
    }

    /**
     * @description Aggiorna i dati per la categoria Medical.
     */
    private static void updateMedicalData(Polizza__c polizza, Map<String, Decimal> medicalData) {
        medicalData.put('count', medicalData.get('count') + 1);
        if (polizza.Importo_Premio_Lordo_Mensile__c != null) {
            medicalData.put('premioMensile', medicalData.get('premioMensile') + polizza.Importo_Premio_Lordo_Mensile__c);
        }
        if (polizza.Importo_Premio_Lordo_Annualizzato__c != null) {
            medicalData.put('premioAnnualizzato', medicalData.get('premioAnnualizzato') + polizza.Importo_Premio_Lordo_Annualizzato__c);
        }
    }

    /**
     * @description Aggiorna i dati per la categoria Residential.
     */
    private static void updateResidentialData(Polizza__c polizza, Map<String, Decimal> residentialData) {
        residentialData.put('count', residentialData.get('count') + 1);
        if (polizza.Importo_Premio_Lordo_Mensile__c != null) {
            residentialData.put('premioMensile', residentialData.get('premioMensile') + polizza.Importo_Premio_Lordo_Mensile__c);
        }
        if (polizza.Importo_Premio_Lordo_Annualizzato__c != null) {
            residentialData.put('premioAnnualizzato', residentialData.get('premioAnnualizzato') + polizza.Importo_Premio_Lordo_Annualizzato__c);
        }
    }

    /**
     * @description Aggiorna i dati per la categoria Travel.
     */
    private static void updateTravelData(Polizza__c polizza, Map<String, Decimal> travelData) {
        travelData.put('count', travelData.get('count') + 1);
        if (polizza.Importo_Premio_Lordo__c != null) {
            travelData.put('premioLordo', travelData.get('premioLordo') + polizza.Importo_Premio_Lordo__c);
        }
    }

    /**
     * @description Aggiorna i dati per la categoria Animal.
     */
    private static void updateAnimalData(Polizza__c polizza, Map<String, Decimal> animalData) {
        animalData.put('count', animalData.get('count') + 1);
        if (polizza.Importo_Premio_Lordo_Mensile__c != null) {
            animalData.put('premioMensile', animalData.get('premioMensile') + polizza.Importo_Premio_Lordo_Mensile__c);
        }
        // Per gli animali, Premio Lordo Annualizzato usa il campo Importo_Premio_Lordo__c
        if (polizza.Importo_Premio_Lordo__c != null) {
            animalData.put('premioAnnualizzato', animalData.get('premioAnnualizzato') + polizza.Importo_Premio_Lordo__c);
        }
    }

    /**
     * @description Aggiunge le categorie attive all'output finale.
     */
    private static void addActiveCategoriestoOutput(Map<String, Map<String, Decimal>> output, Map<String, Map<String, Decimal>> categoryDataMaps) {
        for (String category : categoryDataMaps.keySet()) {
            Map<String, Decimal> categoryData = categoryDataMaps.get(category);
            if (categoryData.get('count') > 0) {
                output.put(category, categoryData);
            }
        }
    }
}