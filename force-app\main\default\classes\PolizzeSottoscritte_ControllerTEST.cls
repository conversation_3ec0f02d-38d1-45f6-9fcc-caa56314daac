@isTest
private class PolizzeSottoscritte_ControllerTEST {

    @isTest static void testConPolizze(){
        Profile p = [SELECT Id FROM Profile WHERE Name='Discovery Profile'];
        User u = new User(Alias = 'standt', Email='<EMAIL>',
            EmailEncodingKey='UTF-8', LastName='Testing', LanguageLocaleKey='en_US',
            LocaleSidKey='en_US', ProfileId = p.Id,
            TimeZoneSidKey='America/Los_Angeles', UserName='<EMAIL>' + System.currentTimeMillis());
    
        // Insert the user and assign the permission set in a separate transaction
        System.runAs(u) {
            PermissionSet ps = [SELECT Id FROM PermissionSet WHERE Name = 'UTENTE_MGA'];
            insert new PermissionSetAssignment(AssigneeId = u.Id, PermissionSetId = ps.Id);
        }

        System.runAs(u) {
        Id rtAccount= Account.sObjectType.getDescribe().getRecordTypeInfosByDeveloperName().get('Contraente').getRecordTypeId();

        Account a = new Account(FirstName='test1', LastName='test1', Codice_Fiscale__c='****************',recordtypeId = rtAccount );
        insert a;
        List<Polizza__c> polizzeList = new List<Polizza__c>();
        List<String> res = new List<String>();
        Polizza__c p1 = new Polizza__c(name='p1', Contraente__c = a.id, Categoria__c='P/A',Numero_di_Polizza__c='np1', Codice_Carta_Fidelity__c='cf1');
        Polizza__c p2 = new Polizza__c(name='p2', Contraente__c = a.id, Categoria__c='Residential - PL',Numero_di_Polizza__c='np2', Codice_Carta_Fidelity__c='cf2');
        Polizza__c p3 = new Polizza__c(name='p3', Contraente__c = a.id, Categoria__c='Travel',Numero_di_Polizza__c='np3', Codice_Carta_Fidelity__c='cf3');
        Polizza__c p4 = new Polizza__c(name='p4', Contraente__c = a.id, Categoria__c='CRDREG',Numero_di_Polizza__c='np4', Codice_Carta_Fidelity__c='cf4');
        Polizza__c p5 = new Polizza__c(name='p5', Contraente__c = a.id, Categoria__c='Pet',Numero_di_Polizza__c='np5', Codice_Carta_Fidelity__c='cf5');
        polizzeList.add(p1);
        polizzeList.add(p2);
        polizzeList.add(p3);
        polizzeList.add(p4);
        polizzeList.add(p5);
        insert polizzeList;

        Test.startTest();
        res=PolizzeSottoscritte_Controller.getPolizzeAttive(a.id);
        Test.stopTest();

        // Verifica che il risultato contenga le categorie attese
        System.assertNotEquals(null, res, 'Result should not be null');
        System.assert(res.contains('Medical'), 'Result should contain Medical');
        System.assert(res.contains('Residential'), 'Result should contain Residential');
        System.assert(res.contains('Travel'), 'Result should contain Travel');
        System.assert(res.contains('Bill Protection'), 'Result should contain Bill Protection');
        System.assert(res.contains('Animal'), 'Result should contain Animal');
        }
    }

    @isTest static void testGetPolizzeAttiveDetails(){
        Profile p = [SELECT Id FROM Profile WHERE Name='Discovery Profile'];
        User u = new User(Alias = 'standt', Email='<EMAIL>',
            EmailEncodingKey='UTF-8', LastName='Testing', LanguageLocaleKey='en_US',
            LocaleSidKey='en_US', ProfileId = p.Id,
            TimeZoneSidKey='America/Los_Angeles', UserName='<EMAIL>' + System.currentTimeMillis());

        // Insert the user and assign the permission set in a separate transaction
        System.runAs(u) {
            PermissionSet ps = [SELECT Id FROM PermissionSet WHERE Name = 'UTENTE_MGA'];
            insert new PermissionSetAssignment(AssigneeId = u.Id, PermissionSetId = ps.Id);
        }

        // Perform the rest of your operations with the created user
        System.runAs(u) {
            Id rtAccount = Account.sObjectType.getDescribe().getRecordTypeInfosByDeveloperName().get('Contraente').getRecordTypeId();

            Account a = new Account(FirstName='test1', LastName='test1', Codice_Fiscale__c='****************',recordtypeId = rtAccount );
            insert a;
            List<Polizza__c> polizzeList = new List<Polizza__c>();
            Map<String, Map<String, Decimal>> res = new Map<String, Map<String, Decimal>>();

            Polizza__c p1 = new Polizza__c(name='p1', Contraente__c = a.id, Categoria__c='P/A',Numero_di_Polizza__c='np1',
                Codice_Carta_Fidelity__c='cf1', Stato__c='Active', Importo_Premio_Lordo_Mensile__c=100.00,
                Importo_Premio_Lordo_Annualizzato__c=1200.00);
            Polizza__c p2 = new Polizza__c(name='p2', Contraente__c = a.id, Categoria__c='Residential - PL',Numero_di_Polizza__c='np2',
                Codice_Carta_Fidelity__c='cf2', Stato__c='Active', Importo_Premio_Lordo_Mensile__c=50.00,
                Importo_Premio_Lordo_Annualizzato__c=600.00);
            Polizza__c p3 = new Polizza__c(name='p3', Contraente__c = a.id, Categoria__c='Travel',Numero_di_Polizza__c='np3',
                Codice_Carta_Fidelity__c='cf3', Stato__c='Active', Importo_Premio_Lordo__c=25.00);
            Polizza__c p4 = new Polizza__c(name='p4', Contraente__c = a.id, Categoria__c='Pet',Numero_di_Polizza__c='np4',
                Codice_Carta_Fidelity__c='cf4', Stato__c='Active', Importo_Premio_Lordo_Mensile__c=30.00,
                Importo_Premio_Lordo__c=360.00);

            polizzeList.add(p1);
            polizzeList.add(p2);
            polizzeList.add(p3);
            polizzeList.add(p4);
            insert polizzeList;

            Test.startTest();
            res=PolizzeSottoscritte_Controller.getPolizzeAttiveDetails(a.id);
            Test.stopTest();

            // Verifica che i dati siano stati restituiti correttamente
            System.assert(res.containsKey('Medical'), 'Medical data should be present');
            System.assert(res.containsKey('Residential'), 'Residential data should be present');
            System.assert(res.containsKey('Travel'), 'Travel data should be present');
            System.assert(res.containsKey('Animal'), 'Animal data should be present');

            // Verifica i conteggi
            System.assertEquals(1, res.get('Medical').get('count'), 'Medical count should be 1');
            System.assertEquals(1, res.get('Residential').get('count'), 'Residential count should be 1');
            System.assertEquals(1, res.get('Travel').get('count'), 'Travel count should be 1');
            System.assertEquals(1, res.get('Animal').get('count'), 'Animal count should be 1');
        }
    }
    @isTest static void testSenzaPolizze(){
        // Create a new user with the necessary permission set
        Profile p = [SELECT Id FROM Profile WHERE Name='Discovery Profile'];
        User u = new User(Alias = 'standt', Email='<EMAIL>',
            EmailEncodingKey='UTF-8', LastName='Testing', LanguageLocaleKey='en_US',
            LocaleSidKey='en_US', ProfileId = p.Id,
            TimeZoneSidKey='America/Los_Angeles', UserName='<EMAIL>' + System.currentTimeMillis());
    
        // Insert the user and assign the permission set in a separate transaction
        System.runAs(u) {
            PermissionSet ps = [SELECT Id FROM PermissionSet WHERE Name = 'UTENTE_MGA'];
            insert new PermissionSetAssignment(AssigneeId = u.Id, PermissionSetId = ps.Id);
        }
    
        // Perform the rest of your operations with the created user
        System.runAs(u) {
            Id rtAccount = Account.sObjectType.getDescribe().getRecordTypeInfosByDeveloperName().get('Contraente').getRecordTypeId();
    
            Account a = new Account(FirstName='test1', LastName='test1', Codice_Fiscale__c='****************', recordtypeId = rtAccount );
            insert a;
    
            List<String> res = new List<String>();
            Test.startTest();
            res = PolizzeSottoscritte_Controller.getPolizzeAttive(a.Id);
            Test.stopTest();

            // Verifica che non ci siano polizze attive
            System.assertEquals(null, res, 'Result should be null when no active policies exist');
        }
    }
    
    
}