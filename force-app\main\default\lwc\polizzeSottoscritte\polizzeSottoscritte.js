import { LightningElement, api, wire } from 'lwc';
import getPolizzeAttive from '@salesforce/apex/PolizzeSottoscritte_Controller.getPolizzeAttive';
import getPolizzeAttiveDetails from '@salesforce/apex/PolizzeSottoscritte_Controller.getPolizzeAttiveDetails';
import iconaCasa from "@salesforce/resourceUrl/icoCasa";
import iconaViaggi from '@salesforce/resourceUrl/icoViaggi';
import iconaInfortuni from '@salesforce/resourceUrl/icoInfortuni';
import iconaCarrello from '@salesforce/resourceUrl/icoCarrello';
import iconaAnimali from '@salesforce/resourceUrl/icoAnimali';
import { NavigationMixin } from 'lightning/navigation';
import { IsConsoleNavigation, getFocusedTabInfo, openSubtab } from 'lightning/platformWorkspaceApi';


export default class PolizzeSottoscritte extends NavigationMixin(LightningElement) {
    @api
    recordId;
    @api
    carrelloProtetto;
    @api
    animali;

    MedicalClass='desaturate scale';
    TravelClass='desaturate scale';
    ResidentialClass='desaturate scale';
    BillProtectionClass='desaturate scale';
    AnimalClass='desaturate scale';
    icoCasa = iconaCasa;
    icoViaggi = iconaViaggi;
    icoInfortuni = iconaInfortuni;
    icoCarrello = iconaCarrello;
    icoAnimali = iconaAnimali;
    @wire(IsConsoleNavigation) isConsoleNavigation;
    workspaceAPI
    pageurl = window.location.origin;
    cmpIdMap = new Map([["Animal",6], ["Residential",8],["Medical",9],["Travel",10],["Bill Protection",14]]);

    // Dati per le polizze attive
    polizzeData = {};

    // Proprietà per visualizzare i dati
    get medicalData() {
        return this.polizzeData.Medical || null;
    }

    get residentialData() {
        return this.polizzeData.Residential || null;
    }

    get travelData() {
        return this.polizzeData.Travel || null;
    }

    get animalData() {
        return this.polizzeData.Animal || null;
    }

    // Metodi per formattare i valori currency
    formatCurrency(value) {
        if (value == null || value === 0) return '0 €';
        return new Intl.NumberFormat('it-IT', {
            style: 'currency',
            currency: 'EUR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(value);
    }

    connectedCallback(){
        this.doInit();
    }

    doInit(){
        // Carica i dati delle polizze attive
        getPolizzeAttive({idAccount:this.recordId}).then((result)=>{
            if (result!=null) {
                result.forEach(res => {
                    switch (res) {
                        case 'Medical':
                            this.MedicalClass='scale';
                            break;
                        case 'Travel':
                            this.TravelClass='scale';
                            break;
                        case 'Residential':
                            this.ResidentialClass='scale';
                            break;
                        case 'Bill Protection':
                            this.BillProtectionClass='scale';
                            break;
                        case 'Animal':
                            this.AnimalClass='scale';
                            break;
                    }
                });
            }
        }).catch(error => {
            console.error('Errore nel caricamento delle polizze attive:', error);
        });

        // Carica i dettagli delle polizze attive
        getPolizzeAttiveDetails({idAccount:this.recordId}).then((result)=>{
            if (result) {
                this.polizzeData = result;
            }
        }).catch(error => {
            console.error('Errore nel caricamento dei dettagli delle polizze:', error);
        });
    }

    navigateToPolizza(event) {
        if (this.isConsoleNavigation) {
            getFocusedTabInfo().then((tabInfo) => {
                    const primaryTabId = tabInfo.isSubtab ? tabInfo.parentTabId : tabInfo.tabId;
                    const viewId=this.cmpIdMap.get(event.target.alt); 
                    openSubtab(
                    primaryTabId,{
                    url:this.pageurl+"/lightning/cmp/force__dynamicRelatedListViewAll?force__flexipageId=Contraente_Record_Page&force__cmpId=lst_dynamicRelatedList"+viewId+"&force__recordId="+this.recordId+"&ws=%2Flightning%2Fr%2FAccount%2F"+this.recordId,
                    focus: true}
                    
                );
            }).catch(() => {
                // Handle navigation error silently
            });
    }
}
    
}